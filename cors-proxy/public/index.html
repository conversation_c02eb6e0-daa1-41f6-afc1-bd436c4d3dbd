<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Notebooks via Proxy</title>
  <style>
    body { font-family: sans-serif; padding: 1rem; }
    pre  { background: #f4f4f4; padding: 1rem; overflow-x: auto; }
  </style>
</head>
<body>
  <h1>rex Notebooks List</h1>
  <pre id="output">Loading…</pre>
  <script>
    fetch('/api/notebooks')
      .then(r => {
        if (!r.ok) throw new Error(`HTTP ${r.status}`);
        return r.json();
      })
      .then(json => {
        document.getElementById('output').textContent =
          JSON.stringify(json, null, 2);
      })
      .catch(err => {
        document.getElementById('output').textContent = `❌ ${err}`;
        console.error(err);
      });
  </script>
</body>
</html>
