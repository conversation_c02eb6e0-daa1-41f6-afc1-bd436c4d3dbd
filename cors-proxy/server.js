import express from 'express';
import cors    from 'cors';
import fetch   from 'node-fetch';

const app = express();

// 1) Allow your browser (localhost) to hit this proxy
app.use(cors());

// 2) Serve the static front-end from ./public
app.use(express.static('public'));

// 3) Your real cookies go here — copy exactly from your curl’s `-b` flag
const COOKIE = 'XSRF-TOKEN=0Uu05yu1VWSvTj2QBnU6vjXVPeO1JoRilFQHLswidPs; oauth2_proxy_kubeflow=t802tN0curJLtaA-McF7PkZNVD2ifp1eo7k6hOgV0lqHSj5moe6PVI6LFi7sZK6kg1k4FiNqkW1EgjbjQVTMT8fYxIumpagIXDO8Bnm0cIMPoKt4bbRozHVJ2YcTBRF7ywtvq47ZC3jGYFMPYV5_OK1wrAQtXZ1mCaTnt45M6-xzjVSmGVXvhlh9ce5VeIpfqC84K_gDyoKlpZQ4GDdmx8pdSP6UtQxQZ0XJsYEdKCG3e2ndQEHjqIxGBjDeiaIfgZtesDiQEEcZ1JJenBECCuoZ0tdF2lsxCpdb3MGzsmW7ln6Kz_r-kqgzUxfXzLJEc9WoqH4TD_nK0LRWXnAoPCWSzm-uUY4_FnHEYB3JehzrvVGdKL6bvq7DPkFrWBcsEqHbIgJqlk4hyt1U7Jirz7fc2opcPP-yM-BVfV3ceGqv9-Ah8wDVg40RvHTVvFsPB2WUIvmCAj97H0Btj8w-Hy_5RHL8PtPvW5bjYpykVEl4Tl8OHWSakv7rFrNrcxu1yJVcQWv9q1DbVyStNs1N1XnRT1uscii6vKrvfOyVVhjOqUASoIFF-M5U04Ouq-jOzvygrxIlKwIioqdvcJKmo0JYI8G_EDIuVFmN9Ohnxk3o5xeumuKDcDmJqQAazSiHjcdCRn2CwbfhkhHpS2T9dwLf6uSnk46Kp-6k9i76SuZ4DvpUKs1WE6SBwzBCrM9nlWpjluFlLQ64YtqS_xYSaFBZ4FXXDgQ9TtcSbauLAJVH-sG1zpV-ntAVVi0PH_oYpH2IbqHFoubo1jJwyO_TREERy7RdRz-mS7mLQa7EdCmgk5RhhIjLEsVghe_3yjtlRQaKG0YfLe58tmU0cG4gPx4uEl0FFcB-bdcLKfILFpl2hBY3M4t1bt_GUWU4kQ971nJt50YH6bwaUxEnSqEKe47HJqSVXqZFd7pP8rsb0hqO6JePM2v_ka3NPnkdADob-SjQu41ELuYQyu7Q_Hm0vLwRe_Ousfqsbm_BFD7og8Drz5CQYqEZIWq4z6TGYu2zVU2_J1DnaFewW8D14lIjSjvik4rRE8REMNTg-HpWwBoRuFXtfKgEImGMHHuex-77pcOcvIThW98ggFMXROtfSCX20y1BVrP9MEngjwH0tYK2j-J_OeMhaebs76B6D3MISAcGuPK4xVLcVzW_bRzqpBhhI872SbMP-BZk1RI64QqCGQZS-EUkGJm6uucAB6Sp3wVYplLwKkh2yQMvCHrXEZ52rW-BQeQAlGDM8qZCUtJdXPD7MlouFqnkkGMQusMo4AIW4L5Z_EW6cAlhasOHz86HYqlFPRV2xgcZMVMfx1QucsMLeXBZU7VMwyoLUOTlUe1CVJfTTnY6ndvb1o5ooEDAu49CkX00ZHBgDmSno58DsK2KqCb82mqrT9NfXYKeygf0E-DXb_qFI8qjrAyuUN3DYJEwdtbG2ICGvTiDPxNdr6tK7gOHoOOa_3xcUnNMW6THIB4nbNAk4aFMMDS4f2eSFAVegU9ej_c0DM6cymzZ8ocWTpp60xOq2Glf5kimXdC0waA7FJG_ehZsrfrlX2tFYzMl2rTVick1fv_jTjt6O0ZNHo0WFecGBeiCLHezQz9k9VmzUNkHi5Vzn6LQa-HGtvF0GKiMPgDM8MYAU8SYMQ1xbtjOlJmbr34Z6oTdDfsR9CQCBGAui2G6ItXXNRQ2sX2NAlzn50B7b-dk55pKUt4jQUCKRj-u9ctqMjwah1FMba7KNU91EoOqnWGIYddNwsv9rx1sANVVP1a4emq1apWiDKyHlZAQJlhwnyQhtQ559F1sq5dJsPMUC04BqQCXNy1MCDIFqazE4w77_7ch11p-WO6ScjNR5puGvgDfHzUwrehfEzPzUffiZRrngydNdEMAbWbAClkycd--CRjWzUZlChO0G5Oy|1754471334|JcAtjfv7XdXUDYKdUKW9suzZpuTSzqz55Ona0S_UC7U=';

// 4) Proxy endpoint
app.get('/api/notebooks', async (req, res) => {
  try {
    const resp = await fetch(
      'http://34.138.180.126/jupyter/api/namespaces/rex/notebooks',
      {
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Referer': 'http://34.138.180.126/jupyter/',
          'Cookie': COOKIE
        }
      }
    );
    const data = await resp.json();
    res.json(data);
  } catch (err) {
    console.error('Proxy error:', err);
    res.status(500).json({ error: err.message });
  }
});

// 5) Handle 404 for undefined routes
app.use((req, res) => {
  console.log(`404 - Route not found: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    error: 'Route not found',
    method: req.method,
    path: req.originalUrl,
    availableRoutes: [
      'GET /',
      'GET /api/notebooks'
    ]
  });
});

app.listen(3000, () => {
  console.log('🚀 Proxy running at http://localhost:3000');
  console.log('Available routes:');
  console.log('  GET / (serves index.html)');
  console.log('  GET /api/notebooks (proxy to Jupyter API)');
});
