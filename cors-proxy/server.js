import express from 'express';
import cors    from 'cors';
import fetch   from 'node-fetch';

const app = express();

// 1) Allow your browser (localhost) to hit this proxy
app.use(cors());

// 2) Serve the static front-end from ./public
app.use(express.static('public'));

// 3) Your real cookies go here — copy exactly from your curl’s `-b` flag
const COOKIE = [
  'XSRF-TOKEN=0Uu05yu1VWSvTj2QBnU6vjXVPeO1JoRilFQHLswidPs',
  'oauth2_proxy_kubeflow=t802tN0curJLtaA-…-CRjWzUZlChO0G5Oy'
].join('; ');

// 4) Proxy endpoint
app.get('/api/notebooks', async (req, res) => {
  try {
    const resp = await fetch(
      'http://**************/jupyter/api/namespaces/rex/notebooks',
      {
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Referer': 'http://**************/jupyter/',
          '<PERSON>ie': CO<PERSON>IE
        }
      }
    );
    const data = await resp.json();
    res.json(data);
  } catch (err) {
    console.error('Proxy error:', err);
    res.status(500).json({ error: err.message });
  }
});

app.listen(3000, () => {
  console.log('🚀 Proxy running at http://localhost:3000');
});
